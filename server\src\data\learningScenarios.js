// Static list of learning scenarios
export const learningScenarios = [
  {
    id: 's1',
    title: 'Greetings & Introductions',
    description: 'Learn how to introduce yourself and greet others',
    level: 'beginner',
    category: 'social',
  },
  {
    id: 's2',
    title: 'Ordering Food',
    description: 'Practice ordering food at a restaurant',
    level: 'beginner',
    category: 'dining',
  },
  {
    id: 's3',
    title: 'Making Plans',
    description: 'Learn how to make plans with friends',
    level: 'intermediate',
    category: 'social',
  },
  {
    id: 's4',
    title: 'Dorm Life',
    description: 'Conversational practice about living in a dormitory, roommates, and daily routines',
    level: 'beginner',
    category: 'education',
  },
  {
    id: 's5',
    title: 'Group Projects',
    description: 'Discuss roles, deadlines, and collaboration in group projects',
    level: 'intermediate',
    category: 'education',
  },
  {
    id: 's6',
    title: 'Class Interactions',
    description: 'Conversations about asking questions, participating, and presenting in class',
    level: 'intermediate',
    category: 'education',
  },
  {
    id: 's7',
    title: 'Buying Tickets',
    description: 'Practice dialogues for buying tickets at stations or cinemas',
    level: 'beginner',
    category: 'travel',
  },
  {
    id: 's8',
    title: 'Going to a Café',
    description: 'Order drinks, ask about menu items, and chat in a café',
    level: 'beginner',
    category: 'dining',
  },
  {
    id: 's9',
    title: 'Job Interviews',
    description: 'Practice common interview questions and responses',
    level: 'advanced',
    category: 'business',
  },
  {
    id: 's10',
    title: 'Meetings',
    description: 'Language for participating in and leading business meetings',
    level: 'advanced',
    category: 'business',
  },
  {
    id: 's11',
    title: 'Resumes',
    description: 'Learn how to write and discuss resume entries and qualifications',
    level: 'advanced',
    category: 'business',
  },
];
