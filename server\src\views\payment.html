<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Processing</title>
    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 400px;
            width: 100%;
        }
        .logo {
            width: 60px;
            height: 60px;
            background: #667eea;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }
        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 24px;
        }
        .plan-details {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
            text-align: left;
        }
        .plan-name {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        .plan-price {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 8px;
        }
        .plan-description {
            color: #666;
            font-size: 14px;
            line-height: 1.4;
        }
        .payment-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            width: 100%;
            margin: 20px 0 10px;
            transition: background 0.3s;
        }
        .payment-btn:hover {
            background: #5a6fd8;
        }
        .payment-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .cancel-btn {
            background: transparent;
            color: #666;
            border: 1px solid #ddd;
            padding: 12px 30px;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            width: 100%;
            transition: all 0.3s;
        }
        .cancel-btn:hover {
            background: #f5f5f5;
        }
        .loading {
            display: none;
            margin: 20px 0;
        }
        .loading.show {
            display: block;
        }
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .error {
            color: #e74c3c;
            background: #ffeaea;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #e74c3c;
            text-align: left;
        }
        .success {
            color: #27ae60;
            background: #eafaf1;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #27ae60;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">U</div>
        <h1>Complete Your Payment</h1>
        
        <div class="plan-details">
            <div class="plan-name" id="planName">Loading...</div>
            <div class="plan-price" id="planPrice">₹0</div>
            <div class="plan-description" id="planDescription">Loading plan details...</div>
        </div>

        <div id="errorMessage" class="error" style="display: none;"></div>
        <div id="successMessage" class="success" style="display: none;"></div>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <div>Processing payment...</div>
        </div>

        <button id="payButton" class="payment-btn" onclick="initiatePayment()">
            Pay Now
        </button>
        
        <button class="cancel-btn" onclick="cancelPayment()">
            Cancel Payment
        </button>
    </div>

    <script>
        // Get order details from URL path and parameters
        const urlParams = new URLSearchParams(window.location.search);
        const pathParts = window.location.pathname.split('/');
        const orderId = pathParts[pathParts.length - 1]; // Get orderId from URL path
        const token = urlParams.get('token');
        
        let orderDetails = null;

        console.log('🔍 Payment page loaded with:', {
            orderId,
            token: token ? 'present' : 'missing',
            tokenLength: token ? token.length : 0,
            currentUrl: window.location.href,
            host: window.location.host,
            pathParts: pathParts
        });

        // Load order details
        async function loadOrderDetails() {
            if (!orderId || !token) {
                console.error('❌ Missing orderId or token:', { orderId, token });
                showError('Invalid payment link. Please try again from the app.');
                return;
            }

            try {
                console.log('🌐 Fetching payment details for orderId:', orderId);
                // Use the same host as the current page for API calls
                // Note: This endpoint expects token as query parameter, not header
                const apiUrl = `${window.location.protocol}//${window.location.host}/api/v1/subscriptions/payment-details/${orderId}?token=${encodeURIComponent(token)}`;
                console.log('🔗 API URL:', apiUrl);

                const response = await fetch(apiUrl);

                console.log('📡 Response status:', response.status);

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('❌ API Error:', response.status, errorText);
                    throw new Error(`API Error: ${response.status} - ${errorText}`);
                }

                const data = await response.json();
                console.log('📊 Payment details response:', data);
                
                if (data.success) {
                    orderDetails = data.data;
                    displayOrderDetails();
                } else {
                    showError(data.message || 'Failed to load payment details.');
                }
            } catch (error) {
                console.error('Error loading order details:', error);
                showError('Failed to load payment details. Please check your internet connection.');
            }
        }

        function displayOrderDetails() {
            const currency = orderDetails.currency || 'INR';
            const currencySymbol = getCurrencySymbol(currency);
            
            document.getElementById('planName').textContent = orderDetails.planDetails.name + ' Plan';
            document.getElementById('planPrice').textContent = `${currencySymbol}${formatAmount(orderDetails.amount, currency)}`;
            document.getElementById('planDescription').textContent = orderDetails.planDetails.description;
        }

        function getCurrencySymbol(currency) {
            const symbols = {
                'INR': '₹',
                'USD': '$',
                'EUR': '€',
                'GBP': '£',
                'JPY': '¥',
                'CNY': '¥',
                'KRW': '₩'
            };
            return symbols[currency] || currency + ' ';
        }

        function formatAmount(amount, currency) {
            // Format amount based on currency
            if (currency === 'JPY' || currency === 'KRW') {
                return Math.round(amount).toLocaleString();
            }
            return amount.toLocaleString(undefined, { minimumFractionDigits: 0, maximumFractionDigits: 2 });
        }

        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            document.getElementById('payButton').disabled = true;
        }

        function showSuccess(message) {
            const successDiv = document.getElementById('successMessage');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
            document.getElementById('payButton').style.display = 'none';
            
            // Redirect back to app after 3 seconds
            setTimeout(() => {
                window.location.href = 'exp://127.0.0.1:8081/--/(main)/subscription?payment=success';
            }, 3000);
        }

        function showLoading(show) {
            const loading = document.getElementById('loading');
            const button = document.getElementById('payButton');
            
            if (show) {
                loading.classList.add('show');
                button.disabled = true;
                button.textContent = 'Processing...';
            } else {
                loading.classList.remove('show');
                button.disabled = false;
                button.textContent = 'Pay Now';
            }
        }

        async function initiatePayment() {
            if (!orderDetails) {
                showError('Order details not loaded. Please refresh the page.');
                return;
            }

            showLoading(true);

            const options = {
                key: orderDetails.razorpayKeyId,
                amount: Math.round(orderDetails.amount * 100), // Amount in paise (already in rupees, convert to paise)
                currency: orderDetails.currency || 'INR',
                name: 'UNextDoor',
                description: `${orderDetails.planDetails.name} Subscription`,
                order_id: orderDetails.razorpayOrderId,
                prefill: {
                    name: orderDetails.userDetails.name,
                    email: orderDetails.userDetails.email,
                    contact: orderDetails.userDetails.contact || ''
                },
                theme: {
                    color: '#6FC935'
                },
                handler: async function(response) {
                    await handlePaymentSuccess(response);
                },
                modal: {
                    ondismiss: function() {
                        showLoading(false);
                        console.log('Payment dialog closed');
                    }
                }
            };

            try {
                const rzp = new Razorpay(options);
                
                rzp.on('payment.failed', function(response) {
                    showLoading(false);
                    showError(`Payment failed: ${response.error.description}`);
                });

                rzp.open();
            } catch (error) {
                showLoading(false);
                showError('Failed to initialize payment. Please try again.');
                console.error('Razorpay initialization error:', error);
            }
        }

        async function handlePaymentSuccess(response) {
            console.log('Payment successful:', response);
            showLoading(true);

            try {
                const verifyResponse = await fetch('/api/v1/subscriptions/verify-payment', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        razorpay_order_id: response.razorpay_order_id,
                        razorpay_payment_id: response.razorpay_payment_id,
                        razorpay_signature: response.razorpay_signature
                    })
                });

                const verifyData = await verifyResponse.json();

                if (verifyData.success) {
                    showSuccess('Payment successful! Your subscription is now active.');

                    // Send message to parent window (React Native WebView)
                    if (window.ReactNativeWebView) {
                        window.ReactNativeWebView.postMessage(JSON.stringify({
                            type: 'PAYMENT_SUCCESS',
                            data: {
                                ...verifyData.data,
                                paymentId: response.razorpay_payment_id,
                                orderId: response.razorpay_order_id,
                                signature: response.razorpay_signature
                            }
                        }));
                    }

                    // Redirect after delay
                    setTimeout(() => {
                        if (window.ReactNativeWebView) {
                            window.ReactNativeWebView.postMessage(JSON.stringify({
                                type: 'CLOSE_PAYMENT'
                            }));
                        } else {
                            window.close();
                        }
                    }, 2000); // Reduced delay for better UX
                } else {
                    const errorMessage = verifyData.message || 'Payment verification failed. Please contact support.';
                    showError(errorMessage);

                    // Send error message to parent window
                    if (window.ReactNativeWebView) {
                        window.ReactNativeWebView.postMessage(JSON.stringify({
                            type: 'PAYMENT_FAILED',
                            data: {
                                error: errorMessage,
                                code: 'VERIFICATION_FAILED',
                                details: verifyData
                            }
                        }));
                    }
                }
            } catch (error) {
                console.error('Payment verification error:', error);
                const errorMessage = 'Payment verification failed. Please contact support.';
                showError(errorMessage);

                // Send error message to parent window
                if (window.ReactNativeWebView) {
                    window.ReactNativeWebView.postMessage(JSON.stringify({
                        type: 'PAYMENT_FAILED',
                        data: {
                            error: errorMessage,
                            code: 'NETWORK_ERROR',
                            details: error.message
                        }
                    }));
                }
            } finally {
                showLoading(false);
            }
        }

        function cancelPayment() {
            // Send cancellation message to React Native WebView
            if (window.ReactNativeWebView) {
                window.ReactNativeWebView.postMessage(JSON.stringify({
                    type: 'PAYMENT_CANCELLED',
                    data: {
                        reason: 'user_cancelled',
                        message: 'Payment was cancelled by user'
                    }
                }));
            } else {
                // Fallback for web browsers
                window.close();
            }
        }

        // Load order details when page loads
        window.onload = function() {
            loadOrderDetails();
        };
    </script>
</body>
</html>
